/**
 * 量子共鸣者 - 物理引擎
 * 处理粒子运动、碰撞检测、力场模拟等物理计算
 */

class PhysicsEngine {
    constructor() {
        this.particles = []; // 粒子数组
        this.forceFields = []; // 力场数组
        this.constraints = []; // 约束数组
        
        // 物理参数
        this.gravity = { x: 0, y: 0 }; // 重力
        this.damping = 0.99; // 阻尼系数
        this.timeStep = 1 / 60; // 时间步长
        this.maxVelocity = 500; // 最大速度
        
        // 碰撞检测优化
        this.spatialGrid = new Map(); // 空间网格
        this.gridSize = 50; // 网格大小
        
        console.log('⚛️ 物理引擎已创建');
    }

    /**
     * 创建粒子
     * @param {Object} config - 粒子配置
     * @returns {Object} 粒子对象
     */
    createParticle(config) {
        const particle = {
            id: this.generateId(),
            x: config.x || 0,
            y: config.y || 0,
            z: config.z || 0,
            vx: config.vx || 0,
            vy: config.vy || 0,
            vz: config.vz || 0,
            ax: 0, // 加速度
            ay: 0,
            az: 0,
            mass: config.mass || 1,
            radius: config.radius || 5,
            charge: config.charge || 0, // 电荷
            frequency: config.frequency || 440, // 共鸣频率
            energy: config.energy || 1, // 能量级别
            isActive: config.isActive !== undefined ? config.isActive : false,
            isStatic: config.isStatic || false, // 静态粒子不受力影响
            color: config.color || '#6c5ce7',
            type: config.type || 'quantum', // 粒子类型
            
            // 量子属性
            resonanceStrength: 0, // 当前共鸣强度
            lastResonanceTime: 0, // 上次共鸣时间
            connections: [], // 连接的其他粒子
            
            // 渲染属性
            opacity: config.opacity || 1,
            scale: config.scale || 1,
            rotation: config.rotation || 0,
            
            // 生命周期
            age: 0,
            maxAge: config.maxAge || Infinity,
            
            // 自定义属性
            userData: config.userData || {}
        };
        
        this.particles.push(particle);
        return particle;
    }

    /**
     * 移除粒子
     * @param {string} particleId - 粒子ID
     */
    removeParticle(particleId) {
        const index = this.particles.findIndex(p => p.id === particleId);
        if (index !== -1) {
            // 清理连接
            const particle = this.particles[index];
            particle.connections.forEach(connId => {
                const connParticle = this.getParticle(connId);
                if (connParticle) {
                    const connIndex = connParticle.connections.indexOf(particleId);
                    if (connIndex !== -1) {
                        connParticle.connections.splice(connIndex, 1);
                    }
                }
            });
            
            this.particles.splice(index, 1);
        }
    }

    /**
     * 获取粒子
     * @param {string} particleId - 粒子ID
     * @returns {Object|null} 粒子对象
     */
    getParticle(particleId) {
        return this.particles.find(p => p.id === particleId) || null;
    }

    /**
     * 创建力场
     * @param {Object} config - 力场配置
     * @returns {Object} 力场对象
     */
    createForceField(config) {
        const forceField = {
            id: this.generateId(),
            x: config.x || 0,
            y: config.y || 0,
            z: config.z || 0,
            type: config.type || 'radial', // 'radial', 'directional', 'vortex'
            strength: config.strength || 100,
            radius: config.radius || 100,
            direction: config.direction || { x: 0, y: -1, z: 0 },
            isActive: config.isActive !== undefined ? config.isActive : true,
            affectsTypes: config.affectsTypes || ['quantum'], // 影响的粒子类型
            userData: config.userData || {}
        };
        
        this.forceFields.push(forceField);
        return forceField;
    }

    /**
     * 更新物理模拟
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        const dt = Math.min(deltaTime, this.timeStep * 2); // 限制最大时间步长
        
        // 清空空间网格
        this.spatialGrid.clear();
        
        // 更新粒子
        this.particles.forEach(particle => {
            if (!particle.isStatic) {
                this.updateParticle(particle, dt);
            }
            this.updateSpatialGrid(particle);
        });
        
        // 处理碰撞
        this.handleCollisions();
        
        // 处理共鸣
        this.handleResonance();
        
        // 清理过期粒子
        this.cleanupParticles();
    }

    /**
     * 更新单个粒子
     * @param {Object} particle - 粒子对象
     * @param {number} dt - 时间增量
     */
    updateParticle(particle, dt) {
        // 重置加速度
        particle.ax = 0;
        particle.ay = 0;
        particle.az = 0;
        
        // 应用重力
        particle.ax += this.gravity.x;
        particle.ay += this.gravity.y;
        
        // 应用力场
        this.applyForceFields(particle);
        
        // 应用阻尼
        particle.vx *= this.damping;
        particle.vy *= this.damping;
        particle.vz *= this.damping;
        
        // 更新速度
        particle.vx += particle.ax * dt;
        particle.vy += particle.ay * dt;
        particle.vz += particle.az * dt;
        
        // 限制最大速度
        const speed = Math.sqrt(particle.vx * particle.vx + particle.vy * particle.vy + particle.vz * particle.vz);
        if (speed > this.maxVelocity) {
            const scale = this.maxVelocity / speed;
            particle.vx *= scale;
            particle.vy *= scale;
            particle.vz *= scale;
        }
        
        // 更新位置
        particle.x += particle.vx * dt;
        particle.y += particle.vy * dt;
        particle.z += particle.vz * dt;
        
        // 更新年龄
        particle.age += dt;
        
        // 更新共鸣强度衰减
        if (particle.resonanceStrength > 0) {
            particle.resonanceStrength = Math.max(0, particle.resonanceStrength - dt * 2);
        }
    }

    /**
     * 应用力场到粒子
     * @param {Object} particle - 粒子对象
     */
    applyForceFields(particle) {
        this.forceFields.forEach(field => {
            if (!field.isActive || !field.affectsTypes.includes(particle.type)) {
                return;
            }
            
            const dx = particle.x - field.x;
            const dy = particle.y - field.y;
            const dz = particle.z - field.z;
            const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
            
            if (distance > field.radius) return;
            
            let fx = 0, fy = 0, fz = 0;
            
            switch (field.type) {
                case 'radial':
                    // 径向力场（引力或斥力）
                    if (distance > 0) {
                        const force = field.strength / (distance * distance);
                        const factor = field.strength > 0 ? -1 : 1; // 正值为引力，负值为斥力
                        fx = factor * force * (dx / distance);
                        fy = factor * force * (dy / distance);
                        fz = factor * force * (dz / distance);
                    }
                    break;
                    
                case 'directional':
                    // 方向性力场
                    const falloff = 1 - (distance / field.radius);
                    fx = field.direction.x * field.strength * falloff;
                    fy = field.direction.y * field.strength * falloff;
                    fz = field.direction.z * field.strength * falloff;
                    break;
                    
                case 'vortex':
                    // 涡旋力场
                    if (distance > 0) {
                        const falloff = 1 - (distance / field.radius);
                        const tangentialForce = field.strength * falloff;
                        fx = -dy * tangentialForce / distance;
                        fy = dx * tangentialForce / distance;
                    }
                    break;
            }
            
            // 应用力到粒子
            particle.ax += fx / particle.mass;
            particle.ay += fy / particle.mass;
            particle.az += fz / particle.mass;
        });
    }

    /**
     * 更新空间网格
     * @param {Object} particle - 粒子对象
     */
    updateSpatialGrid(particle) {
        const gridX = Math.floor(particle.x / this.gridSize);
        const gridY = Math.floor(particle.y / this.gridSize);
        const key = `${gridX},${gridY}`;
        
        if (!this.spatialGrid.has(key)) {
            this.spatialGrid.set(key, []);
        }
        this.spatialGrid.get(key).push(particle);
    }

    /**
     * 处理碰撞检测
     */
    handleCollisions() {
        this.spatialGrid.forEach(particles => {
            for (let i = 0; i < particles.length; i++) {
                for (let j = i + 1; j < particles.length; j++) {
                    this.checkCollision(particles[i], particles[j]);
                }
            }
        });
    }

    /**
     * 检查两个粒子的碰撞
     * @param {Object} p1 - 粒子1
     * @param {Object} p2 - 粒子2
     */
    checkCollision(p1, p2) {
        const dx = p2.x - p1.x;
        const dy = p2.y - p1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const minDistance = p1.radius + p2.radius;
        
        if (distance < minDistance && distance > 0) {
            // 发生碰撞
            this.resolveCollision(p1, p2, dx, dy, distance, minDistance);
        }
    }

    /**
     * 解决碰撞
     * @param {Object} p1 - 粒子1
     * @param {Object} p2 - 粒子2
     * @param {number} dx - x方向距离
     * @param {number} dy - y方向距离
     * @param {number} distance - 距离
     * @param {number} minDistance - 最小距离
     */
    resolveCollision(p1, p2, dx, dy, distance, minDistance) {
        // 分离粒子
        const overlap = minDistance - distance;
        const separationX = (dx / distance) * overlap * 0.5;
        const separationY = (dy / distance) * overlap * 0.5;
        
        if (!p1.isStatic) {
            p1.x -= separationX;
            p1.y -= separationY;
        }
        if (!p2.isStatic) {
            p2.x += separationX;
            p2.y += separationY;
        }
        
        // 弹性碰撞
        if (!p1.isStatic && !p2.isStatic) {
            const totalMass = p1.mass + p2.mass;
            const v1x = ((p1.mass - p2.mass) * p1.vx + 2 * p2.mass * p2.vx) / totalMass;
            const v1y = ((p1.mass - p2.mass) * p1.vy + 2 * p2.mass * p2.vy) / totalMass;
            const v2x = ((p2.mass - p1.mass) * p2.vx + 2 * p1.mass * p1.vx) / totalMass;
            const v2y = ((p2.mass - p1.mass) * p2.vy + 2 * p1.mass * p1.vy) / totalMass;
            
            p1.vx = v1x * 0.8; // 添加能量损失
            p1.vy = v1y * 0.8;
            p2.vx = v2x * 0.8;
            p2.vy = v2y * 0.8;
        }
        
        // 触发碰撞事件
        this.onCollision(p1, p2);
    }

    /**
     * 处理共鸣机制
     */
    handleResonance() {
        const currentTime = Date.now();
        
        this.particles.forEach(particle => {
            if (!particle.isActive) return;
            
            // 查找附近的粒子进行共鸣检测
            const nearbyParticles = this.getNearbyParticles(particle, 100);
            
            nearbyParticles.forEach(nearby => {
                if (nearby.id === particle.id) return;
                
                // 计算共鸣强度
                const resonance = MathUtils.calculateResonance(
                    particle.frequency, 
                    nearby.frequency, 
                    50
                );
                
                if (resonance > 0.5) {
                    // 发生共鸣
                    particle.resonanceStrength = Math.max(particle.resonanceStrength, resonance);
                    nearby.resonanceStrength = Math.max(nearby.resonanceStrength, resonance);
                    
                    // 创建连接
                    if (!particle.connections.includes(nearby.id)) {
                        particle.connections.push(nearby.id);
                        nearby.connections.push(particle.id);
                    }
                    
                    // 触发共鸣事件
                    this.onResonance(particle, nearby, resonance);
                }
            });
        });
    }

    /**
     * 获取附近的粒子
     * @param {Object} particle - 中心粒子
     * @param {number} radius - 搜索半径
     * @returns {Array} 附近的粒子数组
     */
    getNearbyParticles(particle, radius) {
        const nearby = [];
        const gridRadius = Math.ceil(radius / this.gridSize);
        const centerGridX = Math.floor(particle.x / this.gridSize);
        const centerGridY = Math.floor(particle.y / this.gridSize);
        
        for (let gx = centerGridX - gridRadius; gx <= centerGridX + gridRadius; gx++) {
            for (let gy = centerGridY - gridRadius; gy <= centerGridY + gridRadius; gy++) {
                const key = `${gx},${gy}`;
                const gridParticles = this.spatialGrid.get(key);
                
                if (gridParticles) {
                    gridParticles.forEach(p => {
                        const distance = MathUtils.distance(particle.x, particle.y, p.x, p.y);
                        if (distance <= radius) {
                            nearby.push(p);
                        }
                    });
                }
            }
        }
        
        return nearby;
    }

    /**
     * 清理过期粒子
     */
    cleanupParticles() {
        this.particles = this.particles.filter(particle => {
            if (particle.age >= particle.maxAge) {
                // 清理连接
                particle.connections.forEach(connId => {
                    const connParticle = this.getParticle(connId);
                    if (connParticle) {
                        const index = connParticle.connections.indexOf(particle.id);
                        if (index !== -1) {
                            connParticle.connections.splice(index, 1);
                        }
                    }
                });
                return false;
            }
            return true;
        });
    }

    /**
     * 碰撞事件回调
     * @param {Object} p1 - 粒子1
     * @param {Object} p2 - 粒子2
     */
    onCollision(p1, p2) {
        // 可以被外部重写
        console.log(`💥 粒子碰撞: ${p1.id} <-> ${p2.id}`);
    }

    /**
     * 共鸣事件回调
     * @param {Object} p1 - 粒子1
     * @param {Object} p2 - 粒子2
     * @param {number} strength - 共鸣强度
     */
    onResonance(p1, p2, strength) {
        // 可以被外部重写
        console.log(`🎵 粒子共鸣: ${p1.id} <-> ${p2.id}, 强度: ${strength.toFixed(2)}`);
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return 'particle_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 清空所有粒子和力场
     */
    clear() {
        this.particles.length = 0;
        this.forceFields.length = 0;
        this.constraints.length = 0;
        this.spatialGrid.clear();
    }

    /**
     * 获取物理引擎统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            particleCount: this.particles.length,
            forceFieldCount: this.forceFields.length,
            constraintCount: this.constraints.length,
            activeParticles: this.particles.filter(p => p.isActive).length,
            resonatingParticles: this.particles.filter(p => p.resonanceStrength > 0).length
        };
    }
}

// 创建全局物理引擎实例
window.physicsEngine = new PhysicsEngine();
